"""
请求模型定义
"""

from typing import Optional, List, Any, Dict
from pydantic import BaseModel, Field
from app.config.constants import IntentType, DeviceType, VendorType


class Message(BaseModel):
    """LLM消息模型"""
    role: str = Field(..., description="消息角色：user, assistant, system")
    content: str = Field(..., description="消息内容")


class ConfigGenerationRequest(BaseModel):
    """配置生成请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表，支持多轮对话")
    device_type: Optional[str] = Field(None, description="设备类型")
    vendor: Optional[str] = Field(None, description="厂商名称")
    stream: bool = Field(default=True, description="是否流式返回")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "请生成一个BGP配置"}
                ],
                "device_type": "router",
                "vendor": "huawei",
                "stream": True
            }
        }


class CustomizedCapabilityRequest(BaseModel):
    """定制化能力请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "CE端BGP AS号|CE端设备|设备型号..."}
                ],
                "device_type": "router",
                "vendor": "huawei"
            }
        }


class LegacyCustomizedCapabilityRequest(BaseModel):
    """定制化能力请求模型（兼容旧版本）"""

    excel_content: str = Field(..., description="Excel表格内容(已转换为文本)")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "excel_content": "CE端BGP AS号|CE端设备|设备型号...",
                "device_type": "router",
                "vendor": "huawei"
            }
        }


class OpenCapabilityRequest(BaseModel):
    """开放式能力请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "示例Excel: ...\n示例TXT: ...\n需求Excel: ..."}
                ],
                "device_type": "router",
                "vendor": "huawei"
            }
        }


class LegacyOpenCapabilityRequest(BaseModel):
    """开放式能力请求模型（兼容旧版本）"""

    example_excel: str = Field(..., description="示例Excel内容")
    example_txt: str = Field(..., description="示例TXT配置内容")
    requirement_excel: str = Field(..., description="实际需求Excel内容")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "example_excel": "示例Excel内容...",
                "example_txt": "示例配置内容...",
                "requirement_excel": "实际需求Excel内容...",
                "device_type": "router",
                "vendor": "huawei"
            }
        }


class GeneralCapabilityRequest(BaseModel):
    """通用能力请求模型"""

    messages: List[Message] = Field(..., description="对话消息列表")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "messages": [
                    {"role": "user", "content": "如何配置BGP邻居"}
                ],
                "device_type": "router",
                "vendor": "huawei"
            }
        }


class LegacyGeneralCapabilityRequest(BaseModel):
    """通用能力请求模型（兼容旧版本）"""

    user_question: str = Field(..., description="用户问题")
    device_type: str = Field(..., description="设备类型")
    vendor: str = Field(..., description="厂商名称")

    class Config:
        json_schema_extra = {
            "example": {
                "user_question": "如何配置BGP邻居",
                "device_type": "router",
                "vendor": "huawei"
            }
        }


class IntentRecognitionRequest(BaseModel):
    """意图识别请求模型"""
    
    user_input: str = Field(..., description="用户输入文本")
    
    class Config:
        json_schema_extra = {
            "example": {
                "user_input": "请根据这个表格生成配置..."
            }
        }


class FileUploadRequest(BaseModel):
    """文件上传请求模型"""
    
    file_content: bytes = Field(..., description="文件内容")
    file_name: str = Field(..., description="文件名")
    file_type: str = Field(..., description="文件类型")
    
    class Config:
        json_schema_extra = {
            "example": {
                "file_name": "config.xlsx",
                "file_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            }
        }
