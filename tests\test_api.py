"""
API接口测试
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app


client = TestClient(app)


class TestAPI:
    """API测试类"""
    
    def test_health_check(self):
        """测试健康检查接口"""
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "version" in data
        assert "uptime" in data
    
    def test_root_endpoint(self):
        """测试根路径接口"""
        response = client.get("/")
        
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "status" in data
    
    def test_generate_config_general(self):
        """测试通用配置生成接口"""
        request_data = {
            "user_input": "请配置华为路由器的BGP协议",
            "device_type": "router",
            "vendor": "huawei",
            "stream": False
        }
        
        response = client.post("/generate-config", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "config_content" in data
    
    def test_generate_config_customized(self):
        """测试定制化配置生成接口"""
        request_data = {
            "user_input": "CE端BGP AS号|CE端设备|设备型号\n65001|Router1|AR2220",
            "device_type": "router",
            "vendor": "huawei",
            "stream": False
        }
        
        response = client.post("/generate-config", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "config_content" in data
    
    def test_generate_config_invalid_input(self):
        """测试无效输入"""
        request_data = {
            "user_input": "",
            "stream": False
        }
        
        response = client.post("/generate-config", json=request_data)
        
        assert response.status_code == 422  # 验证错误
    
    def test_generate_config_stream(self):
        """测试流式配置生成"""
        request_data = {
            "user_input": "请配置华为路由器的OSPF协议",
            "device_type": "router",
            "vendor": "huawei",
            "stream": True
        }
        
        response = client.post("/generate-config", json=request_data)
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "text/plain; charset=utf-8"
