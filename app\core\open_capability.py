"""
开放式能力模块
基于用户自定义Excel+TXT作为fewshot的配置生成
"""

from typing import Dict, Any, AsyncGenerator
from loguru import logger

from app.config.constants import PROMPT_TEMPLATES
from app.models.request import OpenCapabilityRequest, LegacyOpenCapabilityRequest
from app.utils.llm_client import llm_client


class OpenCapability:
    """开放式能力类"""
    
    def __init__(self):
        self.prompt_template = PROMPT_TEMPLATES["OPEN_CAPABILITY"]
    
    def _extract_content_from_messages(self, messages: list) -> str:
        """从消息列表中提取用户输入内容"""
        # 从后往前查找最新的用户消息
        for message in reversed(messages):
            if message.role == "user":
                return message.content.strip()
        return ""

    def _parse_open_capability_input(self, user_input: str) -> Dict[str, str]:
        """解析开放式能力的用户输入，提取示例和需求数据"""
        # 这里应该实现解析逻辑，提取示例Excel、示例TXT和需求Excel
        # 简化实现，假设用户输入包含所有信息
        return {
            "example_excel": user_input,  # 简化处理
            "example_txt": "",
            "requirement_excel": user_input
        }

    def _build_prompt(self, user_input: str, device_type: str, vendor: str) -> str:
        """构建提示词"""
        # 解析用户输入
        parsed_content = self._parse_open_capability_input(user_input)

        prompt = self.prompt_template.format(
            excel_content=parsed_content.get("example_excel", ""),
            txt_content=parsed_content.get("example_txt", ""),
            requirement_excel=parsed_content.get("requirement_excel", ""),
            device_type=device_type,
            vendor=vendor
        )

        logger.info(f"构建开放式能力提示词，长度: {len(prompt)}")
        return prompt

    def _prepare_llm_messages(self, request: OpenCapabilityRequest) -> list[Dict[str, str]]:
        """准备LLM消息"""
        # 从消息中提取用户输入
        user_input = self._extract_content_from_messages(request.messages)

        # 构建系统提示词
        system_prompt = self._build_prompt(user_input, request.device_type, request.vendor)

        # 构建完整的消息列表
        messages = [{"role": "system", "content": system_prompt}]

        # 添加用户的对话历史
        for msg in request.messages:
            messages.append({"role": msg.role, "content": msg.content})

        return messages

    def _get_llm_params(self) -> Dict[str, Any]:
        """获取LLM参数"""
        return {"temperature": 0.3}  # 降低温度以获得更稳定的输出

    def _create_error_chunk(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应块"""
        return {
            "id": "chatcmpl-open-error",
            "object": "chat.completion.chunk",
            "created": 1677652288,
            "model": "open-capability",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": f"配置生成失败: {error_message}"},
                    "finish_reason": "stop"
                }
            ]
        }

    def _extract_content_from_response(self, response: Dict[str, Any]) -> str:
        """从LLM响应中提取内容"""
        if "choices" in response and len(response["choices"]) > 0:
            content = response["choices"][0]["message"]["content"]
            logger.info("开放式配置生成完成")
            return content
        else:
            raise ValueError("LLM响应格式不正确")

    async def generate_config_stream(
        self,
        request: OpenCapabilityRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）"""
        try:
            # 准备LLM请求
            messages = self._prepare_llm_messages(request)
            llm_params = self._get_llm_params()

            # 调用LLM流式接口
            async for chunk in llm_client.chat_completion_stream(
                messages=messages,
                **llm_params
            ):
                yield chunk

            logger.info("开放式配置生成完成")

        except Exception as e:
            logger.error(f"开放式配置生成失败: {str(e)}")
            yield self._create_error_chunk(str(e))

    async def generate_config_legacy(self, request: LegacyOpenCapabilityRequest) -> str:
        """生成配置（兼容旧版本）"""
        # 转换为新格式
        user_content = f"示例Excel: {request.example_excel}\n示例TXT: {request.example_txt}\n需求Excel: {request.requirement_excel}"
        new_request = OpenCapabilityRequest(
            messages=[{"role": "user", "content": user_content}],
            device_type=request.device_type,
            vendor=request.vendor
        )
        return await self.generate_config(new_request)

    async def generate_config_stream_legacy(
        self,
        request: LegacyOpenCapabilityRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回，兼容旧版本）"""
        # 转换为新格式
        user_content = f"示例Excel: {request.example_excel}\n示例TXT: {request.example_txt}\n需求Excel: {request.requirement_excel}"
        new_request = OpenCapabilityRequest(
            messages=[{"role": "user", "content": user_content}],
            device_type=request.device_type,
            vendor=request.vendor
        )
        async for chunk in self.generate_config_stream(new_request):
            yield chunk

    async def generate_config(self, request: OpenCapabilityRequest) -> str:
        """生成配置（非流式返回）"""
        try:
            # 准备LLM请求
            messages = self._prepare_llm_messages(request)
            llm_params = self._get_llm_params()

            # 调用LLM接口
            response = await llm_client.chat_completion(
                messages=messages,
                stream=False,
                **llm_params
            )

            # 提取生成的内容
            return self._extract_content_from_response(response)

        except Exception as e:
            logger.error(f"开放式配置生成失败: {str(e)}")
            raise
    
    def _validate_request(self, request: OpenCapabilityRequest) -> bool:
        """验证请求参数"""
        if not request.example_excel.strip():
            raise ValueError("示例Excel内容不能为空")
        
        if not request.example_txt.strip():
            raise ValueError("示例TXT内容不能为空")
        
        if not request.requirement_excel.strip():
            raise ValueError("需求Excel内容不能为空")
        
        if not request.device_type.strip():
            raise ValueError("设备类型不能为空")
        
        if not request.vendor.strip():
            raise ValueError("厂商信息不能为空")
        
        return True
    
    async def generate_config_with_validation(
        self, 
        request: OpenCapabilityRequest,
        stream: bool = False
    ) -> str | AsyncGenerator[Dict[str, Any], None]:
        """带验证的配置生成"""
        # 验证请求参数
        self._validate_request(request)
        
        if stream:
            return self.generate_config_stream(request)
        else:
            return await self.generate_config(request)


# 全局开放式能力实例
open_capability = OpenCapability()
