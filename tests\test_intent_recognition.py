"""
意图识别模块测试
"""

import pytest
from app.services.intent_recognition import intent_service
from app.models.request import IntentRecognitionRequest
from app.config.constants import IntentType


class TestIntentRecognition:
    """意图识别测试类"""
    
    @pytest.mark.asyncio
    async def test_customized_intent(self):
        """测试定制化意图识别"""
        request = IntentRecognitionRequest(
            user_input="CE端BGP AS号|CE端设备|设备型号|CE端口|CE端互联IP地址|VLAN|BFD时延"
        )
        
        result = await intent_service.recognize_intent(request)
        
        assert result.success is True
        assert result.intent_type == IntentType.CUSTOMIZED.value
        assert result.confidence > 0.8
    
    @pytest.mark.asyncio
    async def test_general_intent(self):
        """测试通用意图识别"""
        request = IntentRecognitionRequest(
            user_input="请帮我配置华为路由器的BGP协议"
        )
        
        result = await intent_service.recognize_intent(request)
        
        assert result.success is True
        assert result.intent_type == IntentType.GENERAL.value
        assert result.device_type is not None
        assert result.vendor is not None
    
    @pytest.mark.asyncio
    async def test_open_intent(self):
        """测试开放式意图识别"""
        request = IntentRecognitionRequest(
            user_input="""
            示例Excel: 端口|IP地址|VLAN
            示例TXT: interface GigabitEthernet0/0/1
            需求Excel: 端口|IP地址|VLAN
            """
        )
        
        result = await intent_service.recognize_intent(request)
        
        assert result.success is True
        assert result.intent_type == IntentType.OPEN.value
    
    @pytest.mark.asyncio
    async def test_invalid_intent(self):
        """测试无效意图识别"""
        request = IntentRecognitionRequest(
            user_input="今天天气怎么样？"
        )
        
        result = await intent_service.recognize_intent(request)
        
        assert result.success is False
        assert result.intent_type == IntentType.INVALID.value
    
    @pytest.mark.asyncio
    async def test_device_vendor_extraction(self):
        """测试设备类型和厂商提取"""
        request = IntentRecognitionRequest(
            user_input="请配置华为路由器的OSPF协议"
        )
        
        result = await intent_service.recognize_intent(request)
        
        assert result.device_type == "router"
        assert result.vendor == "huawei"
