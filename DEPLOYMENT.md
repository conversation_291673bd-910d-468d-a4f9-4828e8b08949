# 数通设备配置生成智能体 - 部署指南

## 快速开始

### 1. 环境准备

**系统要求:**
- Python 3.12+
- 内存: 至少 2GB
- 磁盘: 至少 1GB 可用空间

**安装依赖:**
```bash
# 安装Python依赖
pip install -r requirements.txt

# 或者使用uv（推荐）
pip install uv
uv sync
```

### 2. 配置环境

**复制配置文件:**
```bash
cp .env.example .env
```

**编辑配置文件 (.env):**
```bash
# 基础配置
APP_NAME=数通设备配置生成智能体
DEBUG=false
HOST=0.0.0.0
PORT=8000

# LLM配置（必须配置）
LLM_BASE_URL=http://************:10010/CUCCAI-llm-hub/chat/completions
LLM_API_KEY=618149eb-d43e-4ddc-b406-b0c0e1efd281
LLM_MODEL=Qwen3-235B-A22B

# 知识库配置（必须配置）
KB_BASE_URL=http://************:10010/CUCCAI-intelligent-agent/vectorSearchApi/
KB_APP_ID=urdDUFiZhKrZi
KB_CATEGORY_ID=153
```

### 3. 启动服务

**开发模式:**
```bash
python start.py
```

**生产模式:**
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

**使用uv运行:**
```bash
uv run python start.py
```

### 4. 验证部署

**健康检查:**
```bash
curl http://localhost:8000/health
```

**测试API:**
```bash
curl -X POST "http://localhost:8000/generate-config" \
     -H "Content-Type: application/json" \
     -d '{
       "user_input": "请配置华为路由器的BGP协议",
       "device_type": "router",
       "vendor": "huawei",
       "stream": false
     }'
```

## Docker部署

### 1. 构建镜像

```bash
docker build -t config-agent-py .
```

### 2. 运行容器

```bash
docker run -d \
  --name config-agent \
  -p 8000:8000 \
  -e LLM_API_KEY=your_api_key \
  -e KB_APP_ID=your_app_id \
  -v $(pwd)/logs:/app/logs \
  config-agent-py
```

### 3. 使用Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f config-agent

# 停止服务
docker-compose down
```

## 生产环境部署

### 1. 使用Nginx反向代理

**nginx.conf 示例:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 2. 使用Systemd服务

**创建服务文件 (/etc/systemd/system/config-agent.service):**
```ini
[Unit]
Description=Config Agent Service
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/config-agent
ExecStart=/opt/config-agent/.venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

**启动服务:**
```bash
sudo systemctl enable config-agent
sudo systemctl start config-agent
sudo systemctl status config-agent
```

### 3. 监控和日志

**查看应用日志:**
```bash
tail -f logs/app.log
tail -f logs/error.log
```

**监控服务状态:**
```bash
# 检查服务状态
curl http://localhost:8000/health

# 查看系统资源
htop
df -h
```

## 性能优化

### 1. 调整工作进程数

```bash
# 根据CPU核心数调整workers
uvicorn app.main:app --workers $(nproc)
```

### 2. 配置缓存

在 `.env` 中添加:
```bash
# Redis缓存（可选）
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600
```

### 3. 数据库连接池

```bash
# 数据库配置（如果使用）
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
```

## 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查找占用端口的进程
netstat -tulpn | grep :8000
# 或
lsof -i :8000

# 杀死进程
kill -9 <PID>
```

2. **依赖安装失败**
```bash
# 清理pip缓存
pip cache purge

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

3. **LLM连接失败**
```bash
# 测试网络连接
curl -I http://************:10010/CUCCAI-llm-hub/chat/completions

# 检查API密钥
echo $LLM_API_KEY
```

4. **内存不足**
```bash
# 查看内存使用
free -h

# 调整工作进程数
uvicorn app.main:app --workers 1
```

### 日志分析

**查看错误日志:**
```bash
grep "ERROR" logs/app.log | tail -20
```

**监控请求:**
```bash
grep "请求" logs/app.log | tail -20
```

## 安全配置

### 1. 防火墙设置

```bash
# 只允许必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 2. SSL证书配置

```bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com
```

### 3. 限制访问

在nginx配置中添加:
```nginx
# 限制请求频率
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

location / {
    limit_req zone=api burst=20 nodelay;
    # ... 其他配置
}
```

## 备份和恢复

### 1. 备份配置

```bash
# 备份配置文件
tar -czf config-backup-$(date +%Y%m%d).tar.gz .env templates/ logs/
```

### 2. 数据备份

```bash
# 备份日志和模板
rsync -av logs/ /backup/logs/
rsync -av templates/ /backup/templates/
```

### 3. 自动备份脚本

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/config-agent"
mkdir -p $BACKUP_DIR

tar -czf $BACKUP_DIR/backup_$DATE.tar.gz \
    .env templates/ logs/ \
    --exclude='logs/*.log'

# 保留最近7天的备份
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete
```

## 更新和维护

### 1. 更新代码

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt --upgrade

# 重启服务
sudo systemctl restart config-agent
```

### 2. 数据库迁移

```bash
# 如果有数据库变更
python manage.py migrate
```

### 3. 清理日志

```bash
# 清理旧日志
find logs/ -name "*.log" -mtime +30 -delete

# 或使用logrotate
sudo logrotate -f /etc/logrotate.d/config-agent
```

## 联系支持

如遇到部署问题，请提供以下信息：
- 操作系统版本
- Python版本
- 错误日志
- 配置文件（脱敏后）

技术支持邮箱: <EMAIL>
