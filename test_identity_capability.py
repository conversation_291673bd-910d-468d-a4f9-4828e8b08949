"""
测试身份识别能力
"""

import asyncio
from app.models.request import ConfigGenerationRequest, Message
from app.services.config_generation import config_generation_service


async def test_identity_capability():
    """测试身份识别能力"""
    print("🧪 开始测试身份识别能力...")
    
    # 测试用例
    test_cases = [
        "你是谁？",
        "你是什么？", 
        "介绍一下你自己",
        "你能做什么？",
        "你的功能有哪些？",
        "你是什么助手？",
        "请介绍你的身份",
        "你是哪个公司的产品？"
    ]
    
    for i, question in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: {question} ---")
        
        # 创建请求
        request = ConfigGenerationRequest(
            messages=[Message(role="user", content=question)],
            device_type="unknown",
            vendor="unknown",
            stream=False
        )
        
        try:
            # 测试非流式响应
            print("📝 非流式响应:")
            result = await config_generation_service.generate_config(request)
            print(f"✅ 成功: {result.config_content[:100]}...")
            
            # 测试流式响应
            print("\n🌊 流式响应:")
            request.stream = True
            chunk_count = 0
            async for chunk in config_generation_service.generate_config_stream(request):
                chunk_count += 1
                print(f"  块 {chunk_count}: {chunk[:50]}...")
                if chunk_count >= 3:  # 只显示前3块
                    print("  ...")
                    break
            
            print(f"✅ 流式响应成功，共接收 {chunk_count}+ 个数据块")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    print("\n🎉 身份识别能力测试完成！")


async def test_identity_intent_recognition():
    """测试身份识别意图识别"""
    print("\n🔍 测试意图识别...")
    
    from app.services.intent_recognition import intent_service
    
    test_questions = [
        "你是谁？",
        "请配置BGP协议",  # 应该识别为general
        "你能做什么？"
    ]
    
    for question in test_questions:
        print(f"\n测试问题: {question}")
        
        request = ConfigGenerationRequest(
            messages=[Message(role="user", content=question)],
            device_type="unknown",
            vendor="unknown"
        )
        
        try:
            result = await intent_service.recognize_intent_from_messages(request)
            print(f"  意图类型: {result.intent_type}")
            print(f"  置信度: {result.confidence}")
            print(f"  推理: {result.reasoning}")
        except Exception as e:
            print(f"  ❌ 意图识别失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(test_identity_capability())
    asyncio.run(test_identity_intent_recognition())
