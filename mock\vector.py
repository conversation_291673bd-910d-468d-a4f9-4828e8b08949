from fastapi import FastAPI, Request
import uvicorn

app = FastAPI()

@app.post("/vector_search")
async def handle_request(request: Request):
    """
    处理特定请求的接口。
    预期接收的 URL、请求头和 JSON 请求体。
    """
    # 这里你可以选择性地打印或验证请求的详细信息
    print(f"Received request on path: {request.url.path}")
    print(f"Request headers: {request.headers}")

    try:
        request_body = await request.json()
        print(f"Request body: {request_body}")
        
        # 验证请求体是否符合预期
        expected_payload = {"appId":"urdDUFiZhKrZi","topK":5,"categoryId":153,"query":"snmp","similarity":0.4}
        if request_body == expected_payload:
            print("Request body matches the expected payload.")
        else:
            print("Warning: Request body does not exactly match the expected payload.")

    except Exception as e:
        print(f"Could not parse request body as JSON: {e}")
        request_body = None # 或者根据需要处理错误

    # 返回固定响应
    return {"message": "这是一个来自 FastAPI 服务的固定响应。"}

if __name__ == "__main__":
    # 运行 FastAPI 应用在 8888 端口
    uvicorn.run(app, host="0.0.0.0", port=8888)