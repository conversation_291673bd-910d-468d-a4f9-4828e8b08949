#!/usr/bin/env python3
"""
完整系统测试脚本
测试所有核心功能和重构后的代码
"""

import asyncio
import json
import time
from typing import Dict, Any, List
from loguru import logger

# 配置日志
logger.remove()
logger.add(
    lambda msg: print(msg, end=""),
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level="INFO"
)


class SystemTester:
    """系统测试器"""
    
    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
    
    def log_test_result(self, test_name: str, success: bool, message: str, details: Dict = None):
        """记录测试结果"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            self.failed_tests += 1
            status = "❌ FAIL"
        
        result = {
            "test_name": test_name,
            "status": status,
            "success": success,
            "message": message,
            "details": details or {},
            "timestamp": time.time()
        }
        
        self.test_results.append(result)
        print(f"{status} | {test_name}: {message}")
        
        if details:
            for key, value in details.items():
                print(f"    {key}: {value}")
    
    async def test_module_imports(self):
        """测试模块导入"""
        try:
            # 测试核心模块导入
            from app.core.general_capability import general_capability
            from app.core.customized_capability import customized_capability
            from app.core.open_capability import open_capability
            from app.core.identity_capability import identity_capability
            from app.core.base_capability import BaseCapability, LLMBasedCapability
            
            # 测试服务模块导入
            from app.services.config_generation import config_generation_service, CapabilityRouter
            from app.services.intent_recognition import intent_service
            
            # 测试模型导入
            from app.models.request import (
                ConfigGenerationRequest, 
                Message,
                GeneralCapabilityRequest,
                CustomizedCapabilityRequest,
                OpenCapabilityRequest
            )
            
            # 测试工具模块导入
            from app.utils.llm_client import llm_client
            from app.utils.knowledge_base import kb_client
            
            details = {
                "核心能力模块": "✅",
                "基础能力类": "✅",
                "服务模块": "✅", 
                "路由器": "✅",
                "请求模型": "✅",
                "工具模块": "✅"
            }
            
            self.log_test_result("模块导入测试", True, "所有模块导入成功", details)
            return True
            
        except Exception as e:
            self.log_test_result("模块导入测试", False, f"模块导入失败: {str(e)}")
            return False
    
    async def test_capability_inheritance(self):
        """测试能力类继承关系"""
        try:
            from app.core.general_capability import general_capability
            from app.core.open_capability import open_capability
            from app.core.customized_capability import customized_capability
            from app.core.identity_capability import identity_capability
            from app.core.base_capability import BaseCapability, LLMBasedCapability
            
            # 检查继承关系
            inheritance_checks = {
                "通用能力继承LLMBasedCapability": isinstance(general_capability, LLMBasedCapability),
                "开放式能力继承LLMBasedCapability": isinstance(open_capability, LLMBasedCapability),
                "定制化能力继承BaseCapability": isinstance(customized_capability, BaseCapability),
                "身份识别能力独立实现": hasattr(identity_capability, 'generate_config')
            }
            
            all_passed = all(inheritance_checks.values())
            
            details = {k: "✅" if v else "❌" for k, v in inheritance_checks.items()}
            
            self.log_test_result(
                "能力类继承测试", 
                all_passed, 
                "继承关系检查完成" if all_passed else "部分继承关系异常",
                details
            )
            return all_passed
            
        except Exception as e:
            self.log_test_result("能力类继承测试", False, f"继承关系检查失败: {str(e)}")
            return False
    
    async def test_capability_router(self):
        """测试能力路由器"""
        try:
            from app.services.config_generation import CapabilityRouter
            from app.models.request import ConfigGenerationRequest, Message
            from app.config.constants import IntentType
            
            router = CapabilityRouter()
            
            # 测试路由映射
            mapping_checks = {
                "定制化能力映射": IntentType.CUSTOMIZED.value in router.capability_mapping,
                "开放式能力映射": IntentType.OPEN.value in router.capability_mapping,
                "通用能力映射": IntentType.GENERAL.value in router.capability_mapping,
                "身份识别能力映射": IntentType.IDENTITY.value in router.capability_mapping
            }
            
            # 测试请求创建
            test_context = {
                'messages': [Message(role="user", content="测试消息")],
                'device_type': 'router',
                'vendor': 'huawei'
            }
            
            request_creation_checks = {}
            for intent_type in router.capability_mapping.keys():
                try:
                    request = router._create_capability_request(intent_type, test_context)
                    request_creation_checks[f"{intent_type}请求创建"] = request is not None
                except Exception as e:
                    request_creation_checks[f"{intent_type}请求创建"] = False
            
            all_checks = {**mapping_checks, **request_creation_checks}
            all_passed = all(all_checks.values())
            
            details = {k: "✅" if v else "❌" for k, v in all_checks.items()}
            
            self.log_test_result(
                "能力路由器测试", 
                all_passed, 
                "路由器功能检查完成" if all_passed else "部分路由器功能异常",
                details
            )
            return all_passed
            
        except Exception as e:
            self.log_test_result("能力路由器测试", False, f"路由器测试失败: {str(e)}")
            return False
    
    async def test_intent_recognition(self):
        """测试意图识别功能"""
        try:
            from app.services.intent_recognition import intent_service
            from app.models.request import ConfigGenerationRequest, Message
            
            test_cases = [
                {
                    "input": "你是谁？",
                    "expected_intent": "identity",
                    "description": "身份识别意图"
                },
                {
                    "input": "请配置BGP协议",
                    "expected_intent": "general", 
                    "description": "通用配置意图"
                },
                {
                    "input": "你能做什么？",
                    "expected_intent": "identity",
                    "description": "能力询问意图"
                }
            ]
            
            recognition_results = {}
            
            for case in test_cases:
                try:
                    request = ConfigGenerationRequest(
                        messages=[Message(role="user", content=case["input"])],
                        device_type="router",
                        vendor="huawei"
                    )
                    
                    result = await intent_service.recognize_intent_from_messages(request)
                    
                    success = (result.success and 
                             result.intent_type == case["expected_intent"])
                    
                    recognition_results[case["description"]] = "✅" if success else "❌"
                    
                except Exception as e:
                    recognition_results[case["description"]] = f"❌ ({str(e)[:50]})"
            
            all_passed = all("✅" in result for result in recognition_results.values())
            
            self.log_test_result(
                "意图识别测试",
                all_passed,
                "意图识别功能检查完成" if all_passed else "部分意图识别异常",
                recognition_results
            )
            return all_passed
            
        except Exception as e:
            self.log_test_result("意图识别测试", False, f"意图识别测试失败: {str(e)}")
            return False
    
    async def test_identity_capability(self):
        """测试身份识别能力"""
        try:
            from app.core.identity_capability import identity_capability
            from app.models.request import ConfigGenerationRequest, Message

            # 测试非流式响应
            request = ConfigGenerationRequest(
                messages=[Message(role="user", content="你是谁？")],
                device_type="router",
                vendor="huawei"
            )

            # 非流式测试
            response = await identity_capability.generate_config(request)
            non_stream_success = isinstance(response, str) and len(response) > 0

            # 流式测试
            stream_chunks = []
            async for chunk in identity_capability.generate_config_stream(request):
                stream_chunks.append(chunk)

            stream_success = len(stream_chunks) > 0 and all(isinstance(chunk, dict) for chunk in stream_chunks)

            details = {
                "非流式响应": "✅" if non_stream_success else "❌",
                "流式响应": "✅" if stream_success else "❌",
                "响应内容长度": len(response) if non_stream_success else 0,
                "流式块数量": len(stream_chunks)
            }

            all_passed = non_stream_success and stream_success

            self.log_test_result(
                "身份识别能力测试",
                all_passed,
                "身份识别能力检查完成" if all_passed else "身份识别能力异常",
                details
            )
            return all_passed

        except Exception as e:
            self.log_test_result("身份识别能力测试", False, f"身份识别能力测试失败: {str(e)}")
            return False

    async def test_config_generation_service(self):
        """测试配置生成服务"""
        try:
            from app.services.config_generation import config_generation_service
            from app.models.request import ConfigGenerationRequest, Message

            # 测试身份识别请求
            identity_request = ConfigGenerationRequest(
                messages=[Message(role="user", content="你是什么助手？")],
                device_type="router",
                vendor="huawei"
            )

            # 非流式测试
            response = await config_generation_service.generate_config(identity_request)
            non_stream_success = (response.success and
                                response.intent_type == "identity" and
                                len(response.config_content) > 0)

            # 流式测试
            stream_chunks = []
            async for chunk in config_generation_service.generate_config_stream(identity_request):
                stream_chunks.append(chunk)

            stream_success = len(stream_chunks) > 0

            details = {
                "非流式服务响应": "✅" if non_stream_success else "❌",
                "流式服务响应": "✅" if stream_success else "❌",
                "意图识别正确": "✅" if response.intent_type == "identity" else "❌",
                "流式块数量": len(stream_chunks)
            }

            all_passed = non_stream_success and stream_success

            self.log_test_result(
                "配置生成服务测试",
                all_passed,
                "配置生成服务检查完成" if all_passed else "配置生成服务异常",
                details
            )
            return all_passed

        except Exception as e:
            self.log_test_result("配置生成服务测试", False, f"配置生成服务测试失败: {str(e)}")
            return False

    async def test_code_deduplication(self):
        """测试代码去重效果"""
        try:
            from app.services.config_generation import CapabilityRouter
            from app.core.base_capability import BaseCapability, LLMBasedCapability
            import inspect

            # 检查路由器是否消除了重复代码
            router = CapabilityRouter()
            router_methods = [method for method in dir(router) if not method.startswith('_')]

            # 检查基础能力类是否提供了通用方法
            base_methods = [method for method in dir(BaseCapability) if not method.startswith('_')]
            llm_base_methods = [method for method in dir(LLMBasedCapability) if not method.startswith('_')]

            # 检查能力类是否继承了基础方法
            from app.core.general_capability import general_capability
            from app.core.open_capability import open_capability
            from app.core.customized_capability import customized_capability

            inheritance_checks = {
                "路由器统一处理": (hasattr(router, 'route_to_capability_non_stream') and
                                hasattr(router, 'route_to_capability_stream')),
                "基础能力类方法": len(base_methods) > 0,
                "LLM基础类方法": len(llm_base_methods) > 0,
                "通用能力继承": hasattr(general_capability, '_create_error_chunk'),
                "开放式能力继承": hasattr(open_capability, '_create_error_chunk'),
                "定制化能力继承": hasattr(customized_capability, '_create_error_chunk')
            }

            all_passed = all(inheritance_checks.values())

            details = {k: "✅" if v else "❌" for k, v in inheritance_checks.items()}
            details["基础类方法数"] = len(base_methods)
            details["LLM基础类方法数"] = len(llm_base_methods)

            self.log_test_result(
                "代码去重测试",
                all_passed,
                "代码重构检查完成" if all_passed else "代码重构存在问题",
                details
            )
            return all_passed

        except Exception as e:
            self.log_test_result("代码去重测试", False, f"代码去重测试失败: {str(e)}")
            return False
    
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "="*80)
        print("🧪 系统测试总结")
        print("="*80)
        print(f"总测试数: {self.total_tests}")
        print(f"通过: {self.passed_tests} ✅")
        print(f"失败: {self.failed_tests} ❌")
        print(f"成功率: {(self.passed_tests/self.total_tests*100):.1f}%" if self.total_tests > 0 else "0%")
        
        if self.failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")
        
        print("\n" + "="*80)


async def main():
    """主测试函数"""
    print("🚀 开始完整系统测试")
    print("="*80)
    
    tester = SystemTester()
    
    # 执行所有测试
    await tester.test_module_imports()
    await tester.test_capability_inheritance()
    await tester.test_capability_router()
    await tester.test_intent_recognition()
    await tester.test_identity_capability()
    await tester.test_config_generation_service()
    await tester.test_code_deduplication()
    
    # 打印总结
    tester.print_summary()
    
    # 返回测试结果
    return tester.passed_tests == tester.total_tests


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
