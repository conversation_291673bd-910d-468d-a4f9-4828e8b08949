#
# 华为路由器配置模板
# 生成时间: {{ generation_time }}
# 设备类型: {{ device_type }}
# 厂商: {{ vendor }}
#

{% for item in data_items %}
#
# 配置项 {{ loop.index }} - {{ item.get('CE端设备', '设备' + loop.index|string) }}
#

{% if item.get('CE端BGP AS号') %}
# BGP AS号配置
bgp {{ item['CE端BGP AS号'] }}
{% endif %}

{% if item.get('CE端互联IP地址') and item.get('CE端口') %}
# 接口配置
interface {{ item['CE端口'] }}
 ip address {{ item['CE端互联IP地址'] }} ***************
 undo shutdown
{% endif %}

{% if item.get('VLAN') %}
# VLAN配置
vlan {{ item['VLAN'] }}
 description {{ item.get('备注', 'Auto generated VLAN') }}
{% endif %}

{% if item.get('vpn-instance') %}
# VPN实例配置
ip vpn-instance {{ item['vpn-instance'] }}
{% if item.get('rt/rd') %}
 route-distinguisher {{ item['rt/rd'] }}
 vpn-target {{ item['rt/rd'] }} export-extcommunity
 vpn-target {{ item['rt/rd'] }} import-extcommunity
{% endif %}
{% endif %}

{% if item.get('BFD时延') %}
# BFD配置
bfd
 interval {{ item['BFD时延'] }}
 min-rx-interval {{ item['BFD时延'] }}
 multiplier 3
{% endif %}

{% endfor %}

#
# 配置结束
#
