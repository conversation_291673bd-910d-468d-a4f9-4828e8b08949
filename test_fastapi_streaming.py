"""
测试FastAPI流式响应问题
"""

import asyncio
import time
import json
from fastapi import FastAPI
from fastapi.responses import StreamingResponse
import uvicorn


app = FastAPI()


async def mock_llm_stream():
    """模拟LLM流式响应"""
    for i in range(10):
        await asyncio.sleep(0.5)  # 模拟每0.5秒返回一个数据块
        chunk = {
            "id": f"chunk-{i}",
            "object": "chat.completion.chunk",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": f"这是第{i+1}个数据块的内容。"},
                    "finish_reason": None if i < 9 else "stop"
                }
            ]
        }
        print(f"生成数据块 {i+1}: {time.time()}")
        yield chunk


async def mock_config_generation_stream():
    """模拟配置生成服务的流式响应"""
    async for chunk in mock_llm_stream():
        yield json.dumps(chunk, ensure_ascii=False)


@app.post("/test-stream-1")
async def test_stream_1():
    """测试方法1：直接返回StreamingResponse"""
    print("开始测试方法1")
    
    async def generate():
        async for chunk in mock_config_generation_stream():
            print(f"FastAPI yield: {time.time()}")
            yield f"data: {chunk}\n\n"
        yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8"
        }
    )


@app.post("/test-stream-2")
async def test_stream_2():
    """测试方法2：使用Server-Sent Events格式"""
    print("开始测试方法2")
    
    async def generate():
        async for chunk in mock_config_generation_stream():
            print(f"FastAPI yield SSE: {time.time()}")
            yield f"data: {chunk}\n\n"
        yield "data: [DONE]\n\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@app.post("/test-stream-3")
async def test_stream_3():
    """测试方法3：简单的文本流"""
    print("开始测试方法3")
    
    async def generate():
        for i in range(10):
            await asyncio.sleep(0.5)
            print(f"FastAPI yield simple: {time.time()}")
            yield f"chunk {i+1}\n"
    
    return StreamingResponse(
        generate(),
        media_type="text/plain"
    )


async def test_client():
    """客户端测试函数"""
    import httpx
    
    print("=== 测试客户端流式接收 ===")
    
    async with httpx.AsyncClient() as client:
        print("测试方法3（简单文本流）...")
        start_time = time.time()
        
        async with client.stream("POST", "http://localhost:8000/test-stream-3") as response:
            chunk_count = 0
            async for chunk in response.aiter_text():
                chunk_count += 1
                current_time = time.time()
                print(f"客户端接收块 {chunk_count}: {current_time - start_time:.2f}s - {chunk.strip()}")
                
                if chunk_count == 1:
                    print(f"✅ 第一个块在 {current_time - start_time:.2f}s 后到达")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "client":
        # 运行客户端测试
        asyncio.run(test_client())
    else:
        # 运行服务器
        print("启动测试服务器...")
        print("运行 'python test_fastapi_streaming.py client' 来测试客户端")
        uvicorn.run(app, host="0.0.0.0", port=8000)
