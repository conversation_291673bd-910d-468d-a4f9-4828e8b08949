"""
定制化能力模块测试
"""

import pytest
from app.core.customized_capability import customized_capability
from app.models.request import CustomizedCapabilityRequest


class TestCustomizedCapability:
    """定制化能力测试类"""
    
    @pytest.mark.asyncio
    async def test_generate_config(self):
        """测试配置生成"""
        request = CustomizedCapabilityRequest(
            excel_content="""CE端BGP AS号|CE端设备|设备型号|CE端口|CE端互联IP地址|VLAN
65001|Router1|AR2220|GE0/0/1|***********|100
65002|Router2|AR2220|GE0/0/2|***********|200""",
            device_type="router",
            vendor="huawei"
        )
        
        result = await customized_capability.generate_config(request)
        
        assert isinstance(result, str)
        assert len(result) > 0
        assert "65001" in result or "65002" in result
    
    @pytest.mark.asyncio
    async def test_generate_config_stream(self):
        """测试流式配置生成"""
        request = CustomizedCapabilityRequest(
            excel_content="""CE端BGP AS号|CE端设备
65001|Router1""",
            device_type="router",
            vendor="huawei"
        )
        
        chunks = []
        async for chunk in customized_capability.generate_config_stream(request):
            chunks.append(chunk)
        
        assert len(chunks) > 0
        assert any("choices" in chunk for chunk in chunks)
    
    def test_parse_excel_content(self):
        """测试Excel内容解析"""
        excel_content = """表头1|表头2|表头3
值1|值2|值3
值4|值5|值6"""
        
        result = customized_capability._parse_excel_content(excel_content)
        
        assert len(result) == 2
        assert result[0]["表头1"] == "值1"
        assert result[1]["表头2"] == "值5"
    
    def test_get_template_name(self):
        """测试模板名称获取"""
        template_name = customized_capability._get_template_name("router", "huawei")
        
        assert isinstance(template_name, str)
        assert template_name.endswith(".j2")
