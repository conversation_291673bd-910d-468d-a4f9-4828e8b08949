"""
通用能力模块
基于RAG的通用配置生成
"""

from typing import Dict, Any, AsyncGenerator
from loguru import logger

from app.config.constants import PROMPT_TEMPLATES
from app.models.request import GeneralCapabilityRequest, LegacyGeneralCapabilityRequest
from app.utils.llm_client import llm_client
from app.utils.knowledge_base import kb_client


class GeneralCapability:
    """通用能力类"""
    
    def __init__(self):
        self.prompt_template = PROMPT_TEMPLATES["GENERAL_CAPABILITY"]
    
    async def _get_reference_docs(
        self, 
        user_question: str, 
        device_type: str, 
        vendor: str
    ) -> str:
        """获取参考文档"""
        try:
            # 使用知识库客户端搜索相关文档
            reference_docs = await kb_client.get_reference_docs(
                query=user_question,
                device_type=device_type,
                vendor=vendor,
                max_docs=3
            )
            
            logger.info(f"获取参考文档成功，文档长度: {len(reference_docs)}")
            return reference_docs
            
        except Exception as e:
            logger.error(f"获取参考文档失败: {str(e)}")
            return "暂时无法获取参考文档，将基于通用知识回答"
    
    def _build_prompt(
        self, 
        user_question: str, 
        reference_docs: str, 
        device_type: str, 
        vendor: str
    ) -> str:
        """构建提示词"""
        prompt = self.prompt_template.format(
            user_question=user_question,
            reference_docs=reference_docs,
            device_type=device_type,
            vendor=vendor
        )
        
        logger.info(f"构建通用能力提示词，长度: {len(prompt)}")
        return prompt
    
    def _extract_user_question_from_messages(self, messages: list) -> str:
        """从消息列表中提取用户问题"""
        # 从后往前查找最新的用户消息
        for message in reversed(messages):
            if message.role == "user":
                return message.content.strip()
        return ""

    async def _prepare_generation_context(self, request: GeneralCapabilityRequest) -> Dict[str, Any]:
        """准备配置生成的上下文"""
        # 从消息中提取用户问题
        user_question = self._extract_user_question_from_messages(request.messages)

        # 获取参考文档
        reference_docs = await self._get_reference_docs(
            user_question,
            request.device_type,
            request.vendor
        )

        # 构建系统提示词
        system_prompt = self.prompt_template.format(
            user_question=user_question,
            reference_docs=reference_docs,
            device_type=request.device_type,
            vendor=request.vendor
        )

        # 构建完整的消息列表，包含系统提示和用户对话历史
        messages = [{"role": "system", "content": system_prompt}]

        # 添加用户的对话历史
        for msg in request.messages:
            messages.append({"role": msg.role, "content": msg.content})

        return {
            "user_question": user_question,
            "reference_docs": reference_docs,
            "messages": messages
        }

    def _get_llm_params(self) -> Dict[str, Any]:
        """获取LLM参数"""
        return {"temperature": 0.5}  # 适中的温度以平衡创造性和准确性

    def _create_error_chunk(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应块"""
        return {
            "id": "chatcmpl-general-error",
            "object": "chat.completion.chunk",
            "created": 1677652288,
            "model": "general-capability",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": f"配置生成失败: {error_message}"},
                    "finish_reason": "stop"
                }
            ]
        }

    def _extract_content_from_response(self, response: Dict[str, Any]) -> str:
        """从LLM响应中提取内容"""
        if "choices" in response and len(response["choices"]) > 0:
            content = response["choices"][0]["message"]["content"]
            logger.info("通用配置生成完成")
            return content
        else:
            raise ValueError("LLM响应格式不正确")

    async def generate_config_stream_legacy(
        self,
        request: LegacyGeneralCapabilityRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回，兼容旧版本）"""
        # 转换为新格式
        new_request = GeneralCapabilityRequest(
            messages=[{"role": "user", "content": request.user_question}],
            device_type=request.device_type,
            vendor=request.vendor
        )
        async for chunk in self.generate_config_stream(new_request):
            yield chunk

    async def generate_config_stream(
        self,
        request: GeneralCapabilityRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）"""
        try:
            # 准备生成上下文
            context = await self._prepare_generation_context(request)
            llm_params = self._get_llm_params()

            # 调用LLM流式接口
            async for chunk in llm_client.chat_completion_stream(
                messages=context["messages"],
                **llm_params
            ):
                yield chunk

            logger.info("通用配置生成完成")

        except Exception as e:
            logger.error(f"通用配置生成失败: {str(e)}")
            yield self._create_error_chunk(str(e))

    async def generate_config_legacy(self, request: LegacyGeneralCapabilityRequest) -> str:
        """生成配置（兼容旧版本）"""
        # 转换为新格式
        new_request = GeneralCapabilityRequest(
            messages=[{"role": "user", "content": request.user_question}],
            device_type=request.device_type,
            vendor=request.vendor
        )
        return await self.generate_config(new_request)

    async def generate_config(self, request: GeneralCapabilityRequest) -> str:
        """生成配置（非流式返回）"""
        try:
            # 准备生成上下文
            context = await self._prepare_generation_context(request)
            llm_params = self._get_llm_params()

            # 调用LLM接口
            response = await llm_client.chat_completion(
                messages=context["messages"],
                stream=False,
                **llm_params
            )

            # 提取生成的内容
            return self._extract_content_from_response(response)

        except Exception as e:
            logger.error(f"通用配置生成失败: {str(e)}")
            raise
    
    def _validate_request(self, request: GeneralCapabilityRequest) -> bool:
        """验证请求参数"""
        if not request.user_question.strip():
            raise ValueError("用户问题不能为空")
        
        if not request.device_type.strip():
            raise ValueError("设备类型不能为空")
        
        if not request.vendor.strip():
            raise ValueError("厂商信息不能为空")
        
        return True
    
    async def generate_config_with_validation(
        self, 
        request: GeneralCapabilityRequest,
        stream: bool = False
    ) -> str | AsyncGenerator[Dict[str, Any], None]:
        """带验证的配置生成"""
        # 验证请求参数
        self._validate_request(request)
        
        if stream:
            return self.generate_config_stream(request)
        else:
            return await self.generate_config(request)
    
    async def search_knowledge_base(
        self, 
        query: str, 
        device_type: str = "", 
        vendor: str = ""
    ) -> Dict[str, Any]:
        """搜索知识库的便捷方法"""
        try:
            if device_type and vendor:
                results = await kb_client.search_by_device_type(
                    query, device_type, vendor
                )
            else:
                results = await kb_client.search(query)
            
            return {
                "success": True,
                "results": results,
                "count": len(results)
            }
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "results": [],
                "count": 0
            }


# 全局通用能力实例
general_capability = GeneralCapability()
