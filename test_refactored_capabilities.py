"""
测试重构后的核心能力模块
"""

import sys
import os
import asyncio

# 添加项目路径到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_customized_capability():
    """测试定制化能力重构"""
    print("=== 测试定制化能力重构 ===")
    
    try:
        from app.core.customized_capability import customized_capability
        from app.models.request import CustomizedCapabilityRequest
        
        request = CustomizedCapabilityRequest(
            excel_content="""CE端BGP AS号|CE端设备|设备型号|CE端口
65001|Router1|AR2220|GE0/0/1
65002|Router2|AR2220|GE0/0/2""",
            device_type="router",
            vendor="huawei"
        )
        
        print("1. 测试非流式生成...")
        config_content = await customized_capability.generate_config(request)
        print(f"   生成配置长度: {len(config_content)}")
        print(f"   配置内容预览: {config_content[:100]}...")
        
        print("2. 测试流式生成...")
        chunks = []
        async for chunk in customized_capability.generate_config_stream(request):
            chunks.append(chunk)
        
        print(f"   流式响应块数: {len(chunks)}")
        print(f"   最后一块: {chunks[-1] if chunks else 'None'}")
        
        print("✅ 定制化能力重构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 定制化能力重构测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_open_capability():
    """测试开放式能力重构"""
    print("\n=== 测试开放式能力重构 ===")
    
    try:
        from app.core.open_capability import open_capability
        from app.models.request import OpenCapabilityRequest
        
        request = OpenCapabilityRequest(
            example_excel="端口|IP地址\nGE0/0/1|***********",
            example_txt="interface GigabitEthernet0/0/1\n ip address *********** *************",
            requirement_excel="端口|IP地址\nGE0/0/2|***********",
            device_type="router",
            vendor="huawei"
        )
        
        print("1. 测试非流式生成...")
        config_content = await open_capability.generate_config(request)
        print(f"   生成配置长度: {len(config_content)}")
        print(f"   配置内容预览: {config_content[:100]}...")
        
        print("2. 测试流式生成...")
        chunks = []
        chunk_count = 0
        async for chunk in open_capability.generate_config_stream(request):
            chunks.append(chunk)
            chunk_count += 1
            if chunk_count >= 5:  # 只收集前5个块以节省时间
                break
        
        print(f"   流式响应块数: {len(chunks)}")
        print(f"   第一块: {chunks[0] if chunks else 'None'}")
        
        print("✅ 开放式能力重构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 开放式能力重构测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_general_capability():
    """测试通用能力重构"""
    print("\n=== 测试通用能力重构 ===")
    
    try:
        from app.core.general_capability import general_capability
        from app.models.request import GeneralCapabilityRequest
        
        request = GeneralCapabilityRequest(
            user_question="请配置华为路由器的BGP协议",
            device_type="router",
            vendor="huawei"
        )
        
        print("1. 测试非流式生成...")
        config_content = await general_capability.generate_config(request)
        print(f"   生成配置长度: {len(config_content)}")
        print(f"   配置内容预览: {config_content[:100]}...")
        
        print("2. 测试流式生成...")
        chunks = []
        chunk_count = 0
        async for chunk in general_capability.generate_config_stream(request):
            chunks.append(chunk)
            chunk_count += 1
            if chunk_count >= 5:  # 只收集前5个块以节省时间
                break
        
        print(f"   流式响应块数: {len(chunks)}")
        print(f"   第一块: {chunks[0] if chunks else 'None'}")
        
        print("✅ 通用能力重构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 通用能力重构测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_code_reuse_analysis():
    """分析代码重用情况"""
    print("\n=== 代码重用分析 ===")
    
    try:
        from app.core.customized_capability import customized_capability
        from app.core.open_capability import open_capability
        from app.core.general_capability import general_capability
        
        # 检查定制化能力的方法
        customized_methods = [method for method in dir(customized_capability) if not method.startswith('_')]
        print(f"定制化能力公共方法: {customized_methods}")
        
        # 检查开放式能力的方法
        open_methods = [method for method in dir(open_capability) if not method.startswith('_')]
        print(f"开放式能力公共方法: {open_methods}")
        
        # 检查通用能力的方法
        general_methods = [method for method in dir(general_capability) if not method.startswith('_')]
        print(f"通用能力公共方法: {general_methods}")
        
        # 检查是否都有generate_config和generate_config_stream方法
        all_have_methods = all([
            'generate_config' in customized_methods and 'generate_config_stream' in customized_methods,
            'generate_config' in open_methods and 'generate_config_stream' in open_methods,
            'generate_config' in general_methods and 'generate_config_stream' in general_methods
        ])
        
        if all_have_methods:
            print("✅ 所有核心能力都有统一的接口方法")
        else:
            print("❌ 核心能力接口不统一")
        
        print("✅ 代码重用分析完成")
        return True
        
    except Exception as e:
        print(f"❌ 代码重用分析失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("开始测试重构后的核心能力模块...\n")
    
    tests = [
        ("定制化能力重构测试", test_customized_capability),
        ("开放式能力重构测试", test_open_capability),
        ("通用能力重构测试", test_general_capability),
        ("代码重用分析", test_code_reuse_analysis),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"重构测试完成: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有重构测试通过！代码重复问题已解决。")
        return True
    else:
        print("❌ 部分测试失败，请检查重构代码。")
        return False

if __name__ == "__main__":
    asyncio.run(main())
