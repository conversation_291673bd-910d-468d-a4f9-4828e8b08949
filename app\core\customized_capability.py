"""
定制化能力模块
基于Excel文本和Jinja2模板生成配置
"""

import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template
from loguru import logger

from app.config.settings import settings
from app.models.request import CustomizedCapabilityRequest
from app.models.response import StreamResponse
from app.utils.text_converter import text_converter


class CustomizedCapability:
    """定制化能力类"""
    
    def __init__(self):
        self.template_dir = Path(settings.template_dir)
        self.template_dir.mkdir(exist_ok=True)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_dir)),
            trim_blocks=True,
            lstrip_blocks=True
        )
    
    def _parse_excel_content(self, excel_content: str) -> List[Dict[str, Any]]:
        """解析Excel文本内容为结构化数据"""
        try:
            lines = excel_content.strip().split('\n')
            if not lines:
                return []
            
            # 第一行作为表头
            headers = [h.strip() for h in lines[0].split('|')]
            data_rows = []
            
            # 解析数据行
            for line in lines[1:]:
                if line.strip():
                    values = [v.strip() for v in line.split('|')]
                    # 确保值的数量与表头匹配
                    while len(values) < len(headers):
                        values.append('')
                    
                    row_data = dict(zip(headers, values[:len(headers)]))
                    data_rows.append(row_data)
            
            logger.info(f"解析Excel内容成功，表头: {headers}, 数据行数: {len(data_rows)}")
            return data_rows
            
        except Exception as e:
            logger.error(f"解析Excel内容失败: {str(e)}")
            raise ValueError(f"Excel内容解析失败: {str(e)}")
    
    def _get_template_name(self, device_type: str, vendor: str) -> str:
        """根据设备类型和厂商获取模板名称"""
        # 模板命名规则: {vendor}_{device_type}.j2
        template_name = f"{vendor}_{device_type}.j2"
        
        # 检查模板是否存在
        template_path = self.template_dir / template_name
        if template_path.exists():
            return template_name
        
        # 如果特定模板不存在，尝试通用模板
        generic_template = f"generic_{device_type}.j2"
        generic_path = self.template_dir / generic_template
        if generic_path.exists():
            return generic_template
        
        # 如果都不存在，返回默认模板
        default_template = "default.j2"
        return default_template
    
    def _create_default_template(self) -> str:
        """创建默认模板"""
        default_template_content = """
# 配置生成时间: {{ generation_time }}
# 设备类型: {{ device_type }}
# 厂商: {{ vendor }}

{% for item in data_items %}
# 配置项 {{ loop.index }}
{% for key, value in item.items() %}
# {{ key }}: {{ value }}
{% endfor %}

{% endfor %}
"""
        
        default_template_path = self.template_dir / "default.j2"
        with open(default_template_path, 'w', encoding='utf-8') as f:
            f.write(default_template_content)
        
        return "default.j2"
    
    def _render_template(
        self, 
        template_name: str, 
        data: Dict[str, Any]
    ) -> str:
        """渲染Jinja2模板"""
        try:
            template = self.jinja_env.get_template(template_name)
            rendered_config = template.render(**data)
            return rendered_config.strip()
            
        except Exception as e:
            logger.error(f"模板渲染失败: {str(e)}")
            # 如果模板渲染失败，创建并使用默认模板
            default_template = self._create_default_template()
            template = self.jinja_env.get_template(default_template)
            return template.render(**data)
    
    def _prepare_generation_data(self, request: CustomizedCapabilityRequest) -> Dict[str, Any]:
        """准备配置生成的数据"""
        # 解析Excel内容
        data_items = self._parse_excel_content(request.excel_content)

        if not data_items:
            raise ValueError("Excel内容为空或格式不正确")

        # 获取模板名称
        template_name = self._get_template_name(request.device_type, request.vendor)

        # 准备模板数据
        template_data = {
            "data_items": data_items,
            "device_type": request.device_type,
            "vendor": request.vendor,
            "generation_time": "{{ now() }}",
            "total_items": len(data_items)
        }

        return {
            "template_name": template_name,
            "template_data": template_data,
            "data_items": data_items
        }

    def _generate_config_content(self, generation_data: Dict[str, Any]) -> str:
        """生成配置内容"""
        template_name = generation_data["template_name"]
        template_data = generation_data["template_data"]

        # 渲染配置
        config_content = self._render_template(template_name, template_data)

        logger.info(f"定制化配置生成完成，使用模板: {template_name}")
        return config_content

    def _create_content_chunks(self, content: str, chunk_size: int = 100) -> AsyncGenerator[Dict[str, Any], None]:
        """将内容分块为流式响应"""
        async def chunk_generator():
            # 分块发送配置内容
            for i in range(0, len(content), chunk_size):
                chunk = content[i:i + chunk_size]

                yield {
                    "id": f"chatcmpl-customized-{i}",
                    "object": "chat.completion.chunk",
                    "created": 1677652288,
                    "model": "customized-capability",
                    "choices": [
                        {
                            "index": 0,
                            "delta": {"content": chunk},
                            "finish_reason": None
                        }
                    ]
                }

            # 发送结束标记
            yield {
                "id": "chatcmpl-customized-end",
                "object": "chat.completion.chunk",
                "created": 1677652288,
                "model": "customized-capability",
                "choices": [
                    {
                        "index": 0,
                        "delta": {},
                        "finish_reason": "stop"
                    }
                ]
            }

        return chunk_generator()

    async def generate_config_stream(
        self,
        request: CustomizedCapabilityRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """生成配置（流式返回）"""
        try:
            # 准备生成数据
            generation_data = self._prepare_generation_data(request)

            # 生成配置内容
            config_content = self._generate_config_content(generation_data)

            # 返回流式响应
            async for chunk in self._create_content_chunks(config_content):
                yield chunk

        except Exception as e:
            logger.error(f"定制化配置生成失败: {str(e)}")
            yield self._create_error_chunk(f"配置生成失败: {str(e)}")

    async def generate_config(self, request: CustomizedCapabilityRequest) -> str:
        """生成配置（非流式返回）"""
        try:
            # 准备生成数据
            generation_data = self._prepare_generation_data(request)

            # 生成配置内容
            config_content = self._generate_config_content(generation_data)

            return config_content

        except Exception as e:
            logger.error(f"定制化配置生成失败: {str(e)}")
            raise
    
    def _create_error_chunk(self, error_message: str) -> Dict[str, Any]:
        """创建错误响应块"""
        return {
            "id": "chatcmpl-error",
            "object": "chat.completion.chunk",
            "created": 1677652288,
            "model": "customized-capability",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": f"错误: {error_message}"},
                    "finish_reason": "stop"
                }
            ]
        }


# 全局定制化能力实例
customized_capability = CustomizedCapability()
