"""
测试配置文件
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_llm_client():
    """模拟LLM客户端"""
    mock_client = MagicMock()
    
    # 模拟非流式响应
    mock_client.chat_completion = AsyncMock(return_value={
        "choices": [
            {
                "message": {
                    "content": "# 模拟配置内容\ninterface GigabitEthernet0/0/1\n ip address *********** *************"
                }
            }
        ]
    })
    
    # 模拟流式响应
    async def mock_stream():
        chunks = [
            {
                "id": "test-1",
                "object": "chat.completion.chunk",
                "choices": [{"index": 0, "delta": {"content": "# 模拟"}, "finish_reason": None}]
            },
            {
                "id": "test-2",
                "object": "chat.completion.chunk",
                "choices": [{"index": 0, "delta": {"content": "配置内容"}, "finish_reason": None}]
            },
            {
                "id": "test-3",
                "object": "chat.completion.chunk",
                "choices": [{"index": 0, "delta": {}, "finish_reason": "stop"}]
            }
        ]
        for chunk in chunks:
            yield chunk
    
    mock_client.chat_completion_stream = AsyncMock(return_value=mock_stream())
    
    return mock_client


@pytest.fixture
def mock_kb_client():
    """模拟知识库客户端"""
    mock_client = MagicMock()
    
    mock_client.search = AsyncMock(return_value=[
        {
            "content": "BGP配置示例：router bgp 65001",
            "title": "BGP配置指南",
            "score": 0.95
        }
    ])
    
    mock_client.get_reference_docs = AsyncMock(return_value="参考文档内容")
    
    return mock_client


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, mock_llm_client, mock_kb_client):
    """设置测试环境"""
    # 替换全局客户端实例
    monkeypatch.setattr("app.utils.llm_client.llm_client", mock_llm_client)
    monkeypatch.setattr("app.utils.knowledge_base.kb_client", mock_kb_client)
    
    # 设置测试配置
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("LLM_TIMEOUT", "10")
    monkeypatch.setenv("KB_TIMEOUT", "10")
