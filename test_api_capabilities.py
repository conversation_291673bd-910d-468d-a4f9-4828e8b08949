#!/usr/bin/env python3
"""
API能力测试脚本
专门测试四个核心能力的API接口功能
"""

import asyncio
import json
import time
from typing import Dict, Any, List
from fastapi.testclient import TestClient
from loguru import logger

# 配置日志
logger.remove()
logger.add(
    lambda msg: print(msg, end=""),
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> - <level>{message}</level>",
    level="INFO"
)


class APICapabilityTester:
    """API能力测试器"""
    
    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
        # 初始化测试客户端
        from app.main import app
        self.client = TestClient(app)
    
    def log_test_result(self, test_name: str, success: bool, message: str, details: Dict = None):
        """记录测试结果"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            self.failed_tests += 1
            status = "❌ FAIL"
        
        result = {
            "test_name": test_name,
            "status": status,
            "success": success,
            "message": message,
            "details": details or {},
            "timestamp": time.time()
        }
        
        self.test_results.append(result)
        print(f"{status} | {test_name}: {message}")
        
        if details:
            for key, value in details.items():
                print(f"    {key}: {value}")
    
    def test_identity_capability_non_stream(self):
        """测试身份识别能力 - 非流式"""
        try:
            payload = {
                "messages": [{"role": "user", "content": "你是谁？"}],
                "device_type": "router",
                "vendor": "huawei",
                "stream": False
            }
            
            response = self.client.post("/generate-config", json=payload)
            
            if response.status_code != 200:
                self.log_test_result(
                    "身份识别能力-非流式", 
                    False, 
                    f"HTTP状态码错误: {response.status_code}"
                )
                return False
            
            data = response.json()
            
            # 验证响应格式
            checks = {
                "包含success字段": "success" in data,
                "success为True": data.get("success", False),
                "包含intent_type字段": "intent_type" in data,
                "intent_type正确": data.get("intent_type") == "identity",
                "包含config_content字段": "config_content" in data,
                "config_content非空": bool(data.get("config_content", "").strip()),
                "响应内容包含助手介绍": "云池数通配置生成助手" in data.get("config_content", "")
            }
            
            all_passed = all(checks.values())
            
            details = {k: "✅" if v else "❌" for k, v in checks.items()}
            details["响应长度"] = len(data.get("config_content", ""))
            
            self.log_test_result(
                "身份识别能力-非流式",
                all_passed,
                "身份识别非流式API测试完成" if all_passed else "身份识别非流式API存在问题",
                details
            )
            return all_passed
            
        except Exception as e:
            self.log_test_result("身份识别能力-非流式", False, f"测试异常: {str(e)}")
            return False
    
    def test_identity_capability_stream(self):
        """测试身份识别能力 - 流式"""
        try:
            payload = {
                "messages": [{"role": "user", "content": "你是什么助手？"}],
                "device_type": "router", 
                "vendor": "huawei",
                "stream": True
            }
            
            response = self.client.post("/generate-config", json=payload)
            
            if response.status_code != 200:
                self.log_test_result(
                    "身份识别能力-流式",
                    False,
                    f"HTTP状态码错误: {response.status_code}"
                )
                return False
            
            # 解析流式响应
            response_text = response.text
            chunks = []
            
            for line in response_text.split('\n'):
                if line.startswith('data: '):
                    try:
                        chunk_data = json.loads(line[6:])  # 移除 'data: ' 前缀
                        chunks.append(chunk_data)
                    except json.JSONDecodeError:
                        continue
            
            # 验证流式响应
            checks = {
                "响应格式正确": response_text.startswith("data:"),
                "包含数据块": len(chunks) > 0,
                "包含开始块": any(chunk.get("choices", [{}])[0].get("delta", {}).get("role") == "assistant" for chunk in chunks),
                "包含结束块": any(chunk.get("choices", [{}])[0].get("finish_reason") == "stop" for chunk in chunks),
                "内容包含助手介绍": any("云池数通配置生成助手" in chunk.get("choices", [{}])[0].get("delta", {}).get("content", "") for chunk in chunks)
            }
            
            all_passed = all(checks.values())
            
            details = {k: "✅" if v else "❌" for k, v in checks.items()}
            details["数据块数量"] = len(chunks)
            
            self.log_test_result(
                "身份识别能力-流式",
                all_passed,
                "身份识别流式API测试完成" if all_passed else "身份识别流式API存在问题",
                details
            )
            return all_passed
            
        except Exception as e:
            self.log_test_result("身份识别能力-流式", False, f"测试异常: {str(e)}")
            return False
    
    def test_general_capability_non_stream(self):
        """测试通用能力 - 非流式"""
        try:
            payload = {
                "messages": [{"role": "user", "content": "请配置BGP协议"}],
                "device_type": "router",
                "vendor": "huawei",
                "stream": False
            }
            
            response = self.client.post("/generate-config", json=payload)
            
            if response.status_code != 200:
                self.log_test_result(
                    "通用能力-非流式",
                    False,
                    f"HTTP状态码错误: {response.status_code}"
                )
                return False
            
            data = response.json()
            
            # 验证响应格式
            checks = {
                "包含success字段": "success" in data,
                "success为True": data.get("success", False),
                "包含intent_type字段": "intent_type" in data,
                "intent_type正确": data.get("intent_type") == "general",
                "包含config_content字段": "config_content" in data,
                "config_content非空": bool(data.get("config_content", "").strip()),
                "响应内容包含BGP配置": "bgp" in data.get("config_content", "").lower()
            }
            
            all_passed = all(checks.values())
            
            details = {k: "✅" if v else "❌" for k, v in checks.items()}
            details["响应长度"] = len(data.get("config_content", ""))
            
            self.log_test_result(
                "通用能力-非流式",
                all_passed,
                "通用能力非流式API测试完成" if all_passed else "通用能力非流式API存在问题",
                details
            )
            return all_passed
            
        except Exception as e:
            self.log_test_result("通用能力-非流式", False, f"测试异常: {str(e)}")
            return False
    
    def test_general_capability_stream(self):
        """测试通用能力 - 流式"""
        try:
            payload = {
                "messages": [{"role": "user", "content": "如何配置OSPF协议？"}],
                "device_type": "router",
                "vendor": "huawei", 
                "stream": True
            }
            
            response = self.client.post("/generate-config", json=payload)
            
            if response.status_code != 200:
                self.log_test_result(
                    "通用能力-流式",
                    False,
                    f"HTTP状态码错误: {response.status_code}"
                )
                return False
            
            # 解析流式响应
            response_text = response.text
            chunks = []
            content_parts = []
            
            for line in response_text.split('\n'):
                if line.startswith('data: '):
                    try:
                        chunk_data = json.loads(line[6:])
                        chunks.append(chunk_data)
                        
                        # 提取内容
                        content = chunk_data.get("choices", [{}])[0].get("delta", {}).get("content", "")
                        if content:
                            content_parts.append(content)
                    except json.JSONDecodeError:
                        continue
            
            full_content = "".join(content_parts)
            
            # 验证流式响应
            checks = {
                "响应格式正确": response_text.startswith("data:"),
                "包含数据块": len(chunks) > 0,
                "包含内容块": len(content_parts) > 0,
                "包含结束块": any(chunk.get("choices", [{}])[0].get("finish_reason") == "stop" for chunk in chunks),
                "内容包含OSPF配置": "ospf" in full_content.lower()
            }
            
            all_passed = all(checks.values())
            
            details = {k: "✅" if v else "❌" for k, v in checks.items()}
            details["数据块数量"] = len(chunks)
            details["内容块数量"] = len(content_parts)
            details["完整内容长度"] = len(full_content)
            
            self.log_test_result(
                "通用能力-流式",
                all_passed,
                "通用能力流式API测试完成" if all_passed else "通用能力流式API存在问题",
                details
            )
            return all_passed
            
        except Exception as e:
            self.log_test_result("通用能力-流式", False, f"测试异常: {str(e)}")
            return False
    
    def test_open_capability_non_stream(self):
        """测试开放式能力 - 非流式"""
        try:
            payload = {
                "messages": [{"role": "user", "content": "参考示例配置VLAN"}],
                "device_type": "switch",
                "vendor": "huawei",
                "stream": False
            }
            
            response = self.client.post("/generate-config", json=payload)
            
            if response.status_code != 200:
                self.log_test_result(
                    "开放式能力-非流式",
                    False,
                    f"HTTP状态码错误: {response.status_code}"
                )
                return False
            
            data = response.json()
            
            # 验证响应格式
            checks = {
                "包含success字段": "success" in data,
                "success为True": data.get("success", False),
                "包含intent_type字段": "intent_type" in data,
                "intent_type正确": data.get("intent_type") == "open",
                "包含config_content字段": "config_content" in data,
                "config_content非空": bool(data.get("config_content", "").strip()),
                "响应内容包含VLAN配置": "vlan" in data.get("config_content", "").lower()
            }
            
            all_passed = all(checks.values())
            
            details = {k: "✅" if v else "❌" for k, v in checks.items()}
            details["响应长度"] = len(data.get("config_content", ""))
            
            self.log_test_result(
                "开放式能力-非流式",
                all_passed,
                "开放式能力非流式API测试完成" if all_passed else "开放式能力非流式API存在问题",
                details
            )
            return all_passed
            
        except Exception as e:
            self.log_test_result("开放式能力-非流式", False, f"测试异常: {str(e)}")
            return False

    def test_open_capability_stream(self):
        """测试开放式能力 - 流式"""
        try:
            payload = {
                "messages": [{"role": "user", "content": "参考模板配置接口"}],
                "device_type": "switch",
                "vendor": "huawei",
                "stream": True
            }

            response = self.client.post("/generate-config", json=payload)

            if response.status_code != 200:
                self.log_test_result(
                    "开放式能力-流式",
                    False,
                    f"HTTP状态码错误: {response.status_code}"
                )
                return False

            # 解析流式响应
            response_text = response.text
            chunks = []
            content_parts = []

            for line in response_text.split('\n'):
                if line.startswith('data: '):
                    try:
                        chunk_data = json.loads(line[6:])
                        chunks.append(chunk_data)

                        # 提取内容
                        content = chunk_data.get("choices", [{}])[0].get("delta", {}).get("content", "")
                        if content:
                            content_parts.append(content)
                    except json.JSONDecodeError:
                        continue

            full_content = "".join(content_parts)

            # 验证流式响应
            checks = {
                "响应格式正确": response_text.startswith("data:"),
                "包含数据块": len(chunks) > 0,
                "包含内容块": len(content_parts) > 0,
                "包含结束块": any(chunk.get("choices", [{}])[0].get("finish_reason") == "stop" for chunk in chunks),
                "内容包含接口配置": "interface" in full_content.lower()
            }

            all_passed = all(checks.values())

            details = {k: "✅" if v else "❌" for k, v in checks.items()}
            details["数据块数量"] = len(chunks)
            details["内容块数量"] = len(content_parts)
            details["完整内容长度"] = len(full_content)

            self.log_test_result(
                "开放式能力-流式",
                all_passed,
                "开放式能力流式API测试完成" if all_passed else "开放式能力流式API存在问题",
                details
            )
            return all_passed

        except Exception as e:
            self.log_test_result("开放式能力-流式", False, f"测试异常: {str(e)}")
            return False

    def test_customized_capability_non_stream(self):
        """测试定制化能力 - 非流式"""
        try:
            # 构造包含表格数据的请求
            table_content = """
            CE端BGP AS号 | CE端设备 | 设备型号 | CE端口 | CE端互联IP地址 | VLAN
            65001 | Router1 | NE40E | GE0/0/1 | ***********/30 | 100
            65002 | Router2 | NE40E | GE0/0/2 | ***********/30 | 200
            """

            payload = {
                "messages": [{"role": "user", "content": f"请根据以下表格生成配置：{table_content}"}],
                "device_type": "router",
                "vendor": "huawei",
                "stream": False
            }

            response = self.client.post("/generate-config", json=payload)

            if response.status_code != 200:
                self.log_test_result(
                    "定制化能力-非流式",
                    False,
                    f"HTTP状态码错误: {response.status_code}"
                )
                return False

            data = response.json()

            # 验证响应格式
            checks = {
                "包含success字段": "success" in data,
                "success为True": data.get("success", False),
                "包含intent_type字段": "intent_type" in data,
                "intent_type正确": data.get("intent_type") == "customized",
                "包含config_content字段": "config_content" in data,
                "config_content非空": bool(data.get("config_content", "").strip())
            }

            all_passed = all(checks.values())

            details = {k: "✅" if v else "❌" for k, v in checks.items()}
            details["响应长度"] = len(data.get("config_content", ""))

            self.log_test_result(
                "定制化能力-非流式",
                all_passed,
                "定制化能力非流式API测试完成" if all_passed else "定制化能力非流式API存在问题",
                details
            )
            return all_passed

        except Exception as e:
            self.log_test_result("定制化能力-非流式", False, f"测试异常: {str(e)}")
            return False

    def test_customized_capability_stream(self):
        """测试定制化能力 - 流式"""
        try:
            # 构造包含表格数据的请求
            table_content = """
            CE端设备 | 设备型号 | VLAN | vpn-instance
            Switch1 | S5700 | 10 | VPN_A
            Switch2 | S5700 | 20 | VPN_B
            """

            payload = {
                "messages": [{"role": "user", "content": f"请根据以下表格生成配置：{table_content}"}],
                "device_type": "switch",
                "vendor": "huawei",
                "stream": True
            }

            response = self.client.post("/generate-config", json=payload)

            if response.status_code != 200:
                self.log_test_result(
                    "定制化能力-流式",
                    False,
                    f"HTTP状态码错误: {response.status_code}"
                )
                return False

            # 解析流式响应
            response_text = response.text
            chunks = []
            content_parts = []

            for line in response_text.split('\n'):
                if line.startswith('data: '):
                    try:
                        chunk_data = json.loads(line[6:])
                        chunks.append(chunk_data)

                        # 提取内容
                        content = chunk_data.get("choices", [{}])[0].get("delta", {}).get("content", "")
                        if content:
                            content_parts.append(content)
                    except json.JSONDecodeError:
                        continue

            full_content = "".join(content_parts)

            # 验证流式响应
            checks = {
                "响应格式正确": response_text.startswith("data:"),
                "包含数据块": len(chunks) > 0,
                "包含内容块": len(content_parts) > 0,
                "包含结束块": any(chunk.get("choices", [{}])[0].get("finish_reason") == "stop" for chunk in chunks)
            }

            all_passed = all(checks.values())

            details = {k: "✅" if v else "❌" for k, v in checks.items()}
            details["数据块数量"] = len(chunks)
            details["内容块数量"] = len(content_parts)
            details["完整内容长度"] = len(full_content)

            self.log_test_result(
                "定制化能力-流式",
                all_passed,
                "定制化能力流式API测试完成" if all_passed else "定制化能力流式API存在问题",
                details
            )
            return all_passed

        except Exception as e:
            self.log_test_result("定制化能力-流式", False, f"测试异常: {str(e)}")
            return False

    def run_all_tests(self):
        """运行所有API能力测试"""
        print("🚀 开始API能力测试")
        print("="*80)

        # 测试所有四个核心能力的流式和非流式场景
        test_methods = [
            self.test_identity_capability_non_stream,
            self.test_identity_capability_stream,
            self.test_general_capability_non_stream,
            self.test_general_capability_stream,
            self.test_open_capability_non_stream,
            self.test_open_capability_stream,
            self.test_customized_capability_non_stream,
            self.test_customized_capability_stream
        ]

        for test_method in test_methods:
            try:
                test_method()
            except Exception as e:
                logger.error(f"测试方法 {test_method.__name__} 执行失败: {str(e)}")
                self.log_test_result(test_method.__name__, False, f"测试执行异常: {str(e)}")

        # 打印测试总结
        self.print_summary()

        return self.passed_tests == self.total_tests

    def print_summary(self):
        """打印测试总结"""
        print("\n" + "="*80)
        print("🧪 API能力测试总结")
        print("="*80)
        print(f"总测试数: {self.total_tests}")
        print(f"通过: {self.passed_tests} ✅")
        print(f"失败: {self.failed_tests} ❌")
        print(f"成功率: {(self.passed_tests/self.total_tests*100):.1f}%" if self.total_tests > 0 else "0%")

        # 按能力分组显示结果
        print("\n📊 分能力测试结果:")
        capabilities = {
            "身份识别能力": ["身份识别能力-非流式", "身份识别能力-流式"],
            "通用能力": ["通用能力-非流式", "通用能力-流式"],
            "开放式能力": ["开放式能力-非流式", "开放式能力-流式"],
            "定制化能力": ["定制化能力-非流式", "定制化能力-流式"]
        }

        for capability_name, test_names in capabilities.items():
            capability_results = [r for r in self.test_results if r["test_name"] in test_names]
            passed = sum(1 for r in capability_results if r["success"])
            total = len(capability_results)
            status = "✅" if passed == total else "❌"
            print(f"  {status} {capability_name}: {passed}/{total}")

        if self.failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test_name']}: {result['message']}")

        print("\n" + "="*80)


def main():
    """主测试函数"""
    tester = APICapabilityTester()
    success = tester.run_all_tests()
    return success


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
