"""
常量定义模块
"""

from enum import Enum


class IntentType(Enum):
    """意图类型枚举"""
    CUSTOMIZED = "customized"  # 定制化能力
    OPEN = "open"  # 开放式能力
    GENERAL = "general"  # 通用能力
    IDENTITY = "identity"  # 身份识别能力
    INVALID = "invalid"  # 无效意图


class DeviceType(Enum):
    """设备类型枚举"""
    ROUTER = "router"  # 路由器
    SWITCH = "switch"  # 交换机
    FIREWALL = "firewall"  # 防火墙
    UNKNOWN = "unknown"  # 未知设备


class VendorType(Enum):
    """厂商类型枚举"""
    HUAWEI = "huawei"  # 华为
    CISCO = "cisco"  # 思科
    H3C = "h3c"  # 华三
    JUNIPER = "juniper"  # 瞻博
    UNKNOWN = "unknown"  # 未知厂商


# 定制化能力表头关键词
CUSTOMIZED_TABLE_HEADERS = [
    "CE端BGP AS号", "CE端设备", "设备型号", "CE端口", "CE端互联IP地址",
    "VLAN", "BFD时延", "vpn-instance", "rt/rd", "接收地址范围",
    "发送地址范围", "AS号", "终端设备", "终端端口", "ER互联IP地址",
    "互联带宽", "VRF name", "vpn申请人", "联系方式", "备注"
]

# 设备配置无关问题的回复
INVALID_QUESTION_RESPONSE = "对不起，请提问与配置生成相关的问题"

# 文件类型
SUPPORTED_FILE_TYPES = {
    ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    ".xls": "application/vnd.ms-excel",
    ".txt": "text/plain",
    ".csv": "text/csv"
}

# HTTP状态码
HTTP_STATUS = {
    "SUCCESS": 200,
    "BAD_REQUEST": 400,
    "UNAUTHORIZED": 401,
    "FORBIDDEN": 403,
    "NOT_FOUND": 404,
    "INTERNAL_ERROR": 500,
    "SERVICE_UNAVAILABLE": 503
}

# 日志级别
LOG_LEVELS = {
    "DEBUG": "DEBUG",
    "INFO": "INFO",
    "WARNING": "WARNING",
    "ERROR": "ERROR",
    "CRITICAL": "CRITICAL"
}

# 提示词模板
PROMPT_TEMPLATES = {
    "OPEN_CAPABILITY": """你是数通设备配置助手，用户上传了excel：{excel_content}，txt：{txt_content}。
数通设备配置会通过excel的内容生成txt的结果，请学习之后模仿该方式，根据实际的需求excel：{requirement_excel}，返回配置代码。
设备类型：{device_type}，厂商：{vendor}""",
    
    "GENERAL_CAPABILITY": """你是数通设备配置助手，请根据用户的问题和参考文档生成配置代码。
用户问题：{user_question}
参考文档：{reference_docs}
设备类型：{device_type}，厂商：{vendor}"""
}

# 正则表达式模式
REGEX_PATTERNS = {
    "IP_ADDRESS": r"\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b",
    "VLAN_ID": r"\bvlan\s+(\d+)\b",
    "AS_NUMBER": r"\bas\s+(\d+)\b",
    "DEVICE_NAME": r"^[a-zA-Z0-9\-_]+$"
}

# 缓存配置
CACHE_CONFIG = {
    "TTL": 3600,  # 缓存过期时间(秒)
    "MAX_SIZE": 1000,  # 最大缓存条目数
    "CLEANUP_INTERVAL": 300  # 清理间隔(秒)
}
