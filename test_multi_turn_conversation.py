"""
测试多轮对话功能
"""

import asyncio
import json
from typing import List, Dict, Any

async def test_multi_turn_general_capability():
    """测试通用能力的多轮对话"""
    print("\n=== 测试通用能力多轮对话 ===")
    
    try:
        from app.services.config_generation import config_generation_service
        from app.models.request import ConfigGenerationRequest, Message
        
        # 第一轮对话：询问BGP配置
        print("1. 第一轮对话：询问BGP配置")
        first_request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content="请帮我配置华为路由器的BGP协议")
            ],
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        first_response = await config_generation_service.generate_config(first_request)
        print(f"   第一轮响应成功: {first_response.success}")
        print(f"   意图类型: {first_response.intent_type}")
        print(f"   配置内容长度: {len(first_response.config_content or '')}")
        
        # 第二轮对话：基于第一轮的回答继续询问
        print("\n2. 第二轮对话：继续询问BGP邻居配置")
        second_request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content="请帮我配置华为路由器的BGP协议"),
                Message(role="assistant", content=first_response.config_content or "BGP配置已生成"),
                Message(role="user", content="如何配置BGP邻居？")
            ],
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        second_response = await config_generation_service.generate_config(second_request)
        print(f"   第二轮响应成功: {second_response.success}")
        print(f"   意图类型: {second_response.intent_type}")
        print(f"   配置内容长度: {len(second_response.config_content or '')}")
        
        # 第三轮对话：进一步细化
        print("\n3. 第三轮对话：询问具体参数")
        third_request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content="请帮我配置华为路由器的BGP协议"),
                Message(role="assistant", content=first_response.config_content or "BGP配置已生成"),
                Message(role="user", content="如何配置BGP邻居？"),
                Message(role="assistant", content=second_response.config_content or "BGP邻居配置已生成"),
                Message(role="user", content="BGP AS号应该设置为多少？")
            ],
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        third_response = await config_generation_service.generate_config(third_request)
        print(f"   第三轮响应成功: {third_response.success}")
        print(f"   意图类型: {third_response.intent_type}")
        print(f"   配置内容长度: {len(third_response.config_content or '')}")
        
        print("✅ 通用能力多轮对话测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 通用能力多轮对话测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_multi_turn_customized_capability():
    """测试定制化能力的多轮对话"""
    print("\n=== 测试定制化能力多轮对话 ===")
    
    try:
        from app.services.config_generation import config_generation_service
        from app.models.request import ConfigGenerationRequest, Message
        
        # 第一轮对话：提供表格数据
        print("1. 第一轮对话：提供表格数据")
        table_data = """CE端BGP AS号|CE端设备|设备型号|CE端口
65001|CE-Router-01|NE40E|GE0/0/1
65002|CE-Router-02|NE40E|GE0/0/2"""
        
        first_request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content=table_data)
            ],
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        first_response = await config_generation_service.generate_config(first_request)
        print(f"   第一轮响应成功: {first_response.success}")
        print(f"   意图类型: {first_response.intent_type}")
        print(f"   配置内容长度: {len(first_response.config_content or '')}")
        
        # 第二轮对话：询问修改
        print("\n2. 第二轮对话：询问如何修改配置")
        second_request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content=table_data),
                Message(role="assistant", content=first_response.config_content or "配置已生成"),
                Message(role="user", content="如何修改第一个设备的AS号为65100？")
            ],
            device_type="router",
            vendor="huawei",
            stream=False
        )
        
        second_response = await config_generation_service.generate_config(second_request)
        print(f"   第二轮响应成功: {second_response.success}")
        print(f"   意图类型: {second_response.intent_type}")
        
        print("✅ 定制化能力多轮对话测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 定制化能力多轮对话测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_multi_turn_stream():
    """测试多轮对话的流式返回"""
    print("\n=== 测试多轮对话流式返回 ===")
    
    try:
        from app.services.config_generation import config_generation_service
        from app.models.request import ConfigGenerationRequest, Message
        
        # 多轮对话的流式请求
        request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content="请配置华为交换机的VLAN"),
                Message(role="assistant", content="VLAN配置已生成"),
                Message(role="user", content="如何添加VLAN 100？")
            ],
            device_type="switch",
            vendor="huawei",
            stream=True
        )
        
        print("开始流式生成...")
        chunk_count = 0
        async for chunk in config_generation_service.generate_config_stream(request):
            chunk_count += 1
            if chunk_count <= 3:  # 只显示前3个块
                print(f"   块 {chunk_count}: {chunk[:100]}...")
            if chunk_count >= 10:  # 限制测试块数
                break
        
        print(f"   总共接收到 {chunk_count} 个数据块")
        print("✅ 多轮对话流式返回测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 多轮对话流式返回测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_intent_recognition_with_messages():
    """测试基于消息格式的意图识别"""
    print("\n=== 测试基于消息格式的意图识别 ===")
    
    try:
        from app.services.intent_recognition import intent_service
        from app.models.request import ConfigGenerationRequest, Message
        
        # 测试通用意图识别
        print("1. 测试通用意图识别")
        general_request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content="请配置华为路由器的OSPF协议")
            ],
            device_type="router",
            vendor="huawei"
        )
        
        result = await intent_service.recognize_intent_from_messages(general_request)
        print(f"   意图类型: {result.intent_type}")
        print(f"   置信度: {result.confidence}")
        print(f"   提取的用户输入: {result.user_input}")
        
        # 测试定制化意图识别
        print("\n2. 测试定制化意图识别")
        customized_request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content="CE端BGP AS号|CE端设备|设备型号")
            ],
            device_type="router",
            vendor="huawei"
        )
        
        result2 = await intent_service.recognize_intent_from_messages(customized_request)
        print(f"   意图类型: {result2.intent_type}")
        print(f"   置信度: {result2.confidence}")
        print(f"   提取的用户输入: {result2.user_input}")
        
        # 测试多轮对话中的意图识别
        print("\n3. 测试多轮对话中的意图识别")
        multi_turn_request = ConfigGenerationRequest(
            messages=[
                Message(role="user", content="请配置BGP"),
                Message(role="assistant", content="BGP配置已生成"),
                Message(role="user", content="如何配置BGP邻居？")
            ],
            device_type="router",
            vendor="huawei"
        )
        
        result3 = await intent_service.recognize_intent_from_messages(multi_turn_request)
        print(f"   意图类型: {result3.intent_type}")
        print(f"   置信度: {result3.confidence}")
        print(f"   提取的用户输入: {result3.user_input}")
        
        print("✅ 基于消息格式的意图识别测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基于消息格式的意图识别测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("开始测试多轮对话功能...")
    
    tests = [
        test_intent_recognition_with_messages,
        test_multi_turn_general_capability,
        test_multi_turn_customized_capability,
        test_multi_turn_stream
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"测试 {test.__name__} 执行失败: {str(e)}")
            results.append(False)
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有多轮对话功能测试通过！")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(main())
