"""
测试流式接口修复
"""

import asyncio
import time
from app.services.config_generation import config_generation_service
from app.models.request import ConfigGenerationRequest, Message


async def test_streaming_response():
    """测试流式响应是否真正流式返回"""
    print("=== 测试流式响应修复 ===")
    
    # 创建测试请求
    request = ConfigGenerationRequest(
        messages=[
            Message(role="user", content="请配置华为路由器的BGP协议")
        ],
        device_type="router",
        vendor="huawei",
        stream=True
    )
    
    print("开始流式生成...")
    start_time = time.time()
    chunk_count = 0
    first_chunk_time = None
    
    try:
        async for chunk in config_generation_service.generate_config_stream(request):
            chunk_count += 1
            current_time = time.time()
            
            if chunk_count == 1:
                first_chunk_time = current_time
                print(f"✅ 第一个数据块在 {first_chunk_time - start_time:.2f}s 后到达")
            
            if chunk_count <= 5:  # 显示前5个块的时间
                print(f"   块 {chunk_count}: {current_time - start_time:.2f}s - {len(chunk)} 字符")
            
            if chunk_count >= 10:  # 限制测试块数
                break
                
    except Exception as e:
        print(f"❌ 流式测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    total_time = time.time() - start_time
    print(f"总共接收到 {chunk_count} 个数据块，总耗时 {total_time:.2f}s")
    
    if first_chunk_time and (first_chunk_time - start_time) < 5.0:
        print("✅ 流式响应正常：第一个数据块快速到达")
        return True
    else:
        print("❌ 流式响应异常：第一个数据块延迟过长")
        return False


async def test_non_streaming_response():
    """测试非流式响应作为对比"""
    print("\n=== 测试非流式响应（对比） ===")
    
    # 创建测试请求
    request = ConfigGenerationRequest(
        messages=[
            Message(role="user", content="请配置华为路由器的BGP协议")
        ],
        device_type="router",
        vendor="huawei",
        stream=False
    )
    
    print("开始非流式生成...")
    start_time = time.time()
    
    try:
        result = await config_generation_service.generate_config(request)
        end_time = time.time()
        
        print(f"✅ 非流式响应完成，耗时 {end_time - start_time:.2f}s")
        print(f"   响应成功: {result.success}")
        print(f"   配置内容长度: {len(result.config_content or '')}")
        return True
        
    except Exception as e:
        print(f"❌ 非流式测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("开始测试流式接口修复...")
    
    # 测试流式响应
    streaming_ok = await test_streaming_response()
    
    # 测试非流式响应作为对比
    non_streaming_ok = await test_non_streaming_response()
    
    print(f"\n=== 测试结果 ===")
    print(f"流式响应: {'✅ 通过' if streaming_ok else '❌ 失败'}")
    print(f"非流式响应: {'✅ 通过' if non_streaming_ok else '❌ 失败'}")
    
    if streaming_ok:
        print("🎉 流式接口修复成功！")
    else:
        print("⚠️  流式接口仍有问题")
    
    return streaming_ok and non_streaming_ok


if __name__ == "__main__":
    asyncio.run(main())
