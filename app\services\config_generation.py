"""
配置生成服务
整合意图识别和三项核心能力
"""

import json
from typing import Dict, Any, AsyncGenerator
from loguru import logger

from app.config.constants import IntentType, INVALID_QUESTION_RESPONSE
from app.models.request import (
    ConfigGenerationRequest, CustomizedCapabilityRequest,
    OpenCapabilityRequest, GeneralCapabilityRequest, IntentRecognitionRequest
)
from app.models.response import ConfigGenerationResponse
from app.services.intent_recognition import intent_service
from app.core.customized_capability import customized_capability
from app.core.open_capability import open_capability
from app.core.general_capability import general_capability


class ConfigGenerationService:
    """配置生成服务类"""

    def __init__(self):
        pass

    async def _prepare_generation_context(self, request: ConfigGenerationRequest) -> Dict[str, Any]:
        """准备配置生成的上下文信息"""
        # 意图识别 - 使用新的消息格式方法
        intent_result = await intent_service.recognize_intent_from_messages(request)

        if not intent_result.success:
            return {
                "success": False,
                "error_message": intent_result.message,
                "intent_result": intent_result
            }

        # 获取设备类型和厂商信息
        device_type = request.device_type or intent_result.device_type or "unknown"
        vendor = request.vendor or intent_result.vendor or "unknown"

        logger.info(
            f"意图识别结果: {intent_result.intent_type}, "
            f"设备类型: {device_type}, 厂商: {vendor}"
        )

        return {
            "success": True,
            "intent_result": intent_result,
            "device_type": device_type,
            "vendor": vendor,
            "messages": request.messages,  # 传递完整的消息列表
            "user_input": intent_result.user_input or ""  # 从意图识别结果中获取用户输入
        }

    async def _route_to_capability_non_stream(self, context: Dict[str, Any]):
        """路由到相应的核心能力（非流式）"""
        intent_type = context["intent_result"].intent_type
        device_type = context["device_type"]
        vendor = context["vendor"]
        messages = context["messages"]

        if intent_type == IntentType.CUSTOMIZED.value:
            # 定制化能力 - 直接使用消息格式
            customized_request = CustomizedCapabilityRequest(
                messages=messages,
                device_type=device_type,
                vendor=vendor
            )
            return await customized_capability.generate_config(customized_request)

        elif intent_type == IntentType.OPEN.value:
            # 开放式能力 - 直接使用消息格式
            open_request = OpenCapabilityRequest(
                messages=messages,
                device_type=device_type,
                vendor=vendor
            )
            return await open_capability.generate_config(open_request)

        elif intent_type == IntentType.GENERAL.value:
            # 通用能力 - 直接使用消息格式
            general_request = GeneralCapabilityRequest(
                messages=messages,
                device_type=device_type,
                vendor=vendor
            )
            return await general_capability.generate_config(general_request)

        else:
            # 无效意图
            raise ValueError(INVALID_QUESTION_RESPONSE)

    def _route_to_capability_stream(self, context: Dict[str, Any]):
        """路由到相应的核心能力（流式）"""
        intent_type = context["intent_result"].intent_type
        device_type = context["device_type"]
        vendor = context["vendor"]
        messages = context["messages"]

        if intent_type == IntentType.CUSTOMIZED.value:
            # 定制化能力 - 直接使用消息格式
            customized_request = CustomizedCapabilityRequest(
                messages=messages,
                device_type=device_type,
                vendor=vendor
            )
            return customized_capability.generate_config_stream(customized_request)

        elif intent_type == IntentType.OPEN.value:
            # 开放式能力 - 直接使用消息格式
            open_request = OpenCapabilityRequest(
                messages=messages,
                device_type=device_type,
                vendor=vendor
            )
            return open_capability.generate_config_stream(open_request)

        elif intent_type == IntentType.GENERAL.value:
            # 通用能力 - 直接使用消息格式
            general_request = GeneralCapabilityRequest(
                messages=messages,
                device_type=device_type,
                vendor=vendor
            )
            return general_capability.generate_config_stream(general_request)

        else:
            # 无效意图
            return self._create_invalid_intent_stream()
    
    async def _create_invalid_intent_stream(self) -> AsyncGenerator[Dict[str, Any], None]:
        """创建无效意图的流式响应"""
        error_chunk = {
            "id": "chatcmpl-invalid",
            "object": "chat.completion.chunk",
            "created": 1677652288,
            "model": "config-generation",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": INVALID_QUESTION_RESPONSE},
                    "finish_reason": "stop"
                }
            ]
        }
        yield error_chunk

    async def _create_error_stream(self, error_message: str) -> AsyncGenerator[Dict[str, Any], None]:
        """创建错误的流式响应"""
        error_chunk = {
            "id": "chatcmpl-error",
            "object": "chat.completion.chunk",
            "created": 1677652288,
            "model": "config-generation",
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": error_message},
                    "finish_reason": "stop"
                }
            ]
        }
        yield error_chunk

    async def generate_config_stream(
        self,
        request: ConfigGenerationRequest
    ) -> AsyncGenerator[str, None]:
        """生成配置（流式返回）"""
        try:
            # 准备生成上下文
            context = await self._prepare_generation_context(request)

            if not context["success"]:
                # 意图识别失败，返回错误信息
                error_chunk = {
                    "id": "chatcmpl-intent-error",
                    "object": "chat.completion.chunk",
                    "created": 1677652288,
                    "model": "config-generation",
                    "choices": [
                        {
                            "index": 0,
                            "delta": {"content": context["error_message"]},
                            "finish_reason": "stop"
                        }
                    ]
                }
                yield json.dumps(error_chunk, ensure_ascii=False)
                return

            # 路由到相应的核心能力
            capability_stream = self._route_to_capability_stream(context)

            async for chunk in capability_stream:
                yield json.dumps(chunk, ensure_ascii=False)

        except Exception as e:
            logger.error(f"配置生成流式处理失败: {str(e)}")
            error_chunk = {
                "id": "chatcmpl-error",
                "object": "chat.completion.chunk",
                "created": 1677652288,
                "model": "config-generation",
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": f"配置生成失败: {str(e)}"},
                        "finish_reason": "stop"
                    }
                ]
            }
            yield json.dumps(error_chunk, ensure_ascii=False)
    
    async def generate_config(
        self,
        request: ConfigGenerationRequest
    ) -> ConfigGenerationResponse:
        """生成配置（非流式返回）"""
        try:
            # 准备生成上下文
            context = await self._prepare_generation_context(request)

            if not context["success"]:
                return ConfigGenerationResponse(
                    success=False,
                    message=context["error_message"],
                    code=400,
                    intent_type=context["intent_result"].intent_type
                )

            # 路由到相应的核心能力
            config_content = await self._route_to_capability_non_stream(context)

            return ConfigGenerationResponse(
                success=True,
                message="配置生成成功",
                code=200,
                config_content=config_content,
                intent_type=context["intent_result"].intent_type,
                device_type=context["device_type"],
                vendor=context["vendor"]
            )

        except Exception as e:
            logger.error(f"配置生成失败: {str(e)}")
            return ConfigGenerationResponse(
                success=False,
                message=f"配置生成失败: {str(e)}",
                code=500
            )
    



# 全局配置生成服务实例
config_generation_service = ConfigGenerationService()
