[project]
name = "config-agent-py-pro"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
   " httpx>=0.25.2",
    "jinja2>=3.1.2",
    "pandas>=2.1.4",
    "openpyxl>=3.1.2",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.1",
    "loguru>=0.7.2",
    "python-dotenv>=1.0.0",
    "tenacity>=8.2.3",
    "asyncio-throttle>=1.0.2",
    "pydantic_settings==2.10.1",

    # 开发环境依赖（可选）
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
   " mypy>=1.7.1",
    "pre-commit>=3.6.0"
]
